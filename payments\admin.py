from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import (
    PaymentConfiguration, PaymentTransaction, RazorpayPayment,
    CODPayment, PaymentRefund, PaymentWebhook
)


@admin.register(PaymentConfiguration)
class PaymentConfigurationAdmin(admin.ModelAdmin):
    """
    Admin interface for payment configuration.
    """
    list_display = [
        'active_environment', 'enable_razorpay', 'enable_cod',
        'razorpay_status', 'updated_at'
    ]
    fieldsets = (
        ('Environment Settings', {
            'fields': ('active_environment',),
            'description': 'Select which environment to use for payments'
        }),
        ('Payment Methods', {
            'fields': ('enable_razorpay', 'enable_cod'),
            'description': 'Enable or disable payment methods'
        }),
        ('Razorpay Test Configuration', {
            'fields': ('razorpay_test_key_id', 'razorpay_test_key_secret'),
            'classes': ('collapse',),
            'description': 'Test environment credentials for Razorpay'
        }),
        ('Razorpay Live Configuration', {
            'fields': ('razorpay_live_key_id', 'razorpay_live_key_secret'),
            'classes': ('collapse',),
            'description': 'Live environment credentials for Razorpay'
        }),
        ('Webhook Configuration', {
            'fields': ('razorpay_webhook_secret',),
            'classes': ('collapse',),
            'description': 'Webhook secret for payment notifications'
        }),
        ('COD Settings', {
            'fields': ('cod_charge_percentage', 'cod_minimum_order'),
            'description': 'Cash on Delivery configuration'
        }),
    )

    def has_add_permission(self, request):
        # Only allow one configuration
        return not PaymentConfiguration.objects.exists()

    def has_delete_permission(self, request, obj=None):
        # Don't allow deletion of payment configuration
        return False

    def razorpay_status(self, obj):
        """Show Razorpay configuration status"""
        credentials = obj.get_razorpay_credentials()
        if credentials['key_id'] and credentials['key_secret']:
            return format_html(
                '<span style="color: green;">✓ Configured ({})</span>',
                obj.get_active_environment_display()
            )
        else:
            return format_html(
                '<span style="color: red;">✗ Not Configured ({})</span>',
                obj.get_active_environment_display()
            )
    razorpay_status.short_description = 'Razorpay Status'


@admin.register(PaymentTransaction)
class PaymentTransactionAdmin(admin.ModelAdmin):
    """
    Admin interface for payment transactions.
    """
    list_display = [
        'transaction_id', 'order_number', 'user_email', 'payment_method',
        'amount', 'currency', 'status', 'created_at'
    ]
    list_filter = [
        'payment_method', 'status', 'currency', 'created_at'
    ]
    search_fields = [
        'transaction_id', 'order_number', 'user__email',
        'gateway_transaction_id', 'gateway_payment_id'
    ]
    readonly_fields = [
        'transaction_id', 'created_at', 'updated_at', 'completed_at',
        'refunded_at'
    ]

    fieldsets = (
        ('Transaction Details', {
            'fields': (
                'transaction_id', 'order_id', 'order_number', 'user',
                'payment_method', 'amount', 'currency', 'status'
            )
        }),
        ('Gateway Information', {
            'fields': (
                'gateway_transaction_id', 'gateway_payment_id',
                'gateway_signature', 'gateway_response'
            ),
            'classes': ('collapse',)
        }),
        ('Refund Information', {
            'fields': ('refund_amount',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': (
                'created_at', 'updated_at', 'completed_at',
                'refunded_at'
            ),
            'classes': ('collapse',)
        }),
        ('Failure Details', {
            'fields': ('failure_reason', 'failure_code'),
            'classes': ('collapse',)
        }),
    )

    def user_email(self, obj):
        return obj.user.email if obj.user else 'N/A'
    user_email.short_description = 'User Email'

    def has_add_permission(self, request):
        # Transactions should be created through API only
        return False


@admin.register(RazorpayPayment)
class RazorpayPaymentAdmin(admin.ModelAdmin):
    """
    Admin interface for Razorpay payment details.
    """
    list_display = [
        'razorpay_order_id', 'razorpay_payment_id', 'transaction_status',
        'webhook_verified', 'created_at'
    ]
    list_filter = ['webhook_verified', 'created_at']
    search_fields = [
        'razorpay_order_id', 'razorpay_payment_id', 'razorpay_signature'
    ]
    readonly_fields = ['created_at', 'updated_at']

    def transaction_status(self, obj):
        return obj.transaction.status
    transaction_status.short_description = 'Transaction Status'

    def has_add_permission(self, request):
        return False


@admin.register(CODPayment)
class CODPaymentAdmin(admin.ModelAdmin):
    """
    Admin interface for COD payment details.
    """
    list_display = [
        'transaction_id', 'collected_amount', 'is_collected',
        'collected_by', 'collected_at'
    ]
    list_filter = ['collected_at']
    search_fields = ['transaction__transaction_id', 'collected_by__email']
    readonly_fields = ['created_at', 'updated_at']

    def transaction_id(self, obj):
        return obj.transaction.transaction_id
    transaction_id.short_description = 'Transaction ID'

    def has_add_permission(self, request):
        return False

    def is_collected(self, obj):
        """Check if COD payment is collected"""
        return bool(obj.collected_at)
    is_collected.boolean = True
    is_collected.short_description = 'Collected'
